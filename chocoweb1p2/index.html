<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Chocolate & Art Show — Dallas | September 18-19, 2025</title>
  <meta name="description"
    content="Immersive art, live music, body painting, and artisan chocolate. Two nights in Dallas at Lofty Spaces. 21+ event featuring local artists and DJs.">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Font loading -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@800&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Monoton&display=swap" rel="stylesheet">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="assets/images/choco-logo.png">

  <style>
    :root{
      --pink:#ff2ebd;
      --pink-2:#ff7ad6;
      --white:#ffffff;
      --bg:#000;
      --fg:#eef2ff;
      --card-bg:#1a1a1a;
      --card-bd:#333;
      --name:#fff;
    }

    * { box-sizing: border-box; }
    html, body { height: 100%; margin: 0; }
    
    body{
      background: var(--bg);
      color: var(--white);
      font-family: "Inter", system-ui, sans-serif;
    }

    /* Navigation */
    .nav {
      position: fixed;
      top: 0;
      width: 100%;
      z-index: 1000;
      background: rgba(0,0,0,0.9);
      backdrop-filter: blur(10px);
      padding: 1rem 0;
    }

    .nav-container {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 2rem;
    }

    .nav-logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--white);
      text-decoration: none;
      font-weight: 600;
    }

    .nav-menu {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 2rem;
    }

    .nav-menu a {
      color: var(--white);
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .nav-menu a:hover {
      color: var(--pink);
    }

    /* NEON INTRO SECTION */
    .neon-intro {
      min-height: 100vh;
      display: grid;
      place-items: center;
      background: var(--bg);
      position: relative;
      overflow: hidden;
      padding-top: 80px; /* Account for fixed nav */
    }

    /* Video Background */
    .video-background {
      position: absolute;
      inset: 0;
      z-index: 1;
    }

    .video-background video {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      filter: brightness(0.3);
    }

    .neon-content {
      position: relative;
      z-index: 2;
      display: grid;
      place-items: center;
      gap: clamp(0.4rem, 2.2vw, 1.25rem);
      text-align: center;
      padding: 6rem 2rem;
    }

    /* Neon Sign Styles */
    .neon-line{
      --stroke: rgba(255,255,255,0.85);
      font-family: "Monoton", ui-sans-serif, system-ui;
      font-weight: 400;
      line-height: 0.88;
      text-transform: uppercase;
      white-space: nowrap;
      color: var(--white);
      text-rendering: optimizeLegibility;
      font-kerning: normal;
      text-shadow:
        0 0 .02em var(--white),
        0 0 .08em var(--white),
        0 0 .18em var(--pink),
        0 0 .36em var(--pink),
        0 0 .72em var(--pink),
        0 0 1.2em var(--pink-2);
      -webkit-text-stroke: .01em var(--stroke);
      position: relative;
      isolation: isolate;
      animation: flickerLoop 3.6s linear infinite, glow 6s ease-in-out infinite;
    }

    .line-1{ 
      font-size: clamp(2.8rem, 10.6vw, 10rem);
      letter-spacing: clamp(-.05em, -0.40vw, -.10em);
      word-spacing: clamp(-.04em, -0.30vw, -.08em);
    }
    .line-2{ 
      font-size: clamp(2.6rem, 9.8vw, 9rem);
      letter-spacing: clamp(-.05em, -0.34vw, -.10em);
      word-spacing: clamp(0.02em, 0.2vw, 0.12em);
    }
    .line-3{ 
      font-size: clamp(2.4rem, 8.8vw, 8rem);
      letter-spacing: clamp(-.08em, -0.50vw, -.14em);
      word-spacing: clamp(-.06em, -0.40vw, -.12em);
    }

    .neon-line::before,
    .neon-line::after{
      content: attr(data-text);
      position: absolute; 
      inset: 0; 
      z-index: -1;
      color: var(--pink);
      filter: blur(.12em);
      opacity: .55;
    }
    .neon-line::after{ 
      filter: blur(.45em); 
      opacity: .35; 
    }

    @keyframes flickerLoop{
      0%,100% { opacity:1 }
      2%   { opacity:.25 }
      3%   { opacity:1 }
      5%   { opacity:.4 }
      8%   { opacity:1 }
      10%  { opacity:.35 }
      12%  { opacity:1 }
      25%  { opacity:.9 }
      28%  { opacity:.5 }
      30%  { opacity:1 }
      60%  { opacity:.92 }
      62%  { opacity:.55 }
      70%  { opacity:1 }
      90%  { opacity:.97 }
    }

    @keyframes glow{
      0%,100% { text-shadow:
        0 0 .03em var(--white),
        0 0 .06em var(--white),
        0 0 .15em var(--pink),
        0 0 .32em var(--pink),
        0 0 .6em  var(--pink),
        0 0 1.1em var(--pink-2); }
      50% { text-shadow:
        0 0 .02em var(--white),
        0 0 .04em var(--white),
        0 0 .12em var(--pink),
        0 0 .26em var(--pink),
        0 0 .48em var(--pink),
        0 0 .9em  var(--pink-2); }
    }

    /* TICKET BUTTONS SECTION */
    .ticket-section {
      padding: 4rem 2rem;
      background: var(--bg);
      text-align: center;
    }

    .ticket-buttons {
      display: flex;
      justify-content: center;
      gap: 3rem;
      flex-wrap: wrap;
    }

    /* RGB Style for "BUY TICKETS" text - same as rgb-word */
    .buy-tickets-text {
      position: relative;
      display: inline-block;
      font-weight: 900;
      text-transform: uppercase;
      letter-spacing: -0.02em;
      line-height: 0.85;
      color: #eaeaea;
      font-size: clamp(2.5rem, 8vw, 6rem);
      margin-bottom: 1rem;
    }

    .buy-tickets-text::before,
    .buy-tickets-text::after {
      content: attr(data-txt);
      position: absolute;
      inset: 0;
      z-index: -1;
    }

    .buy-tickets-text::before {
      transform: translate(0.18em, 0.06em);
      color: #e53935;
      opacity: 0.8;
    }

    .buy-tickets-text::after {
      transform: translate(-0.14em, 0.08em);
      color: #03a9f4;
      opacity: 0.75;
    }

    /* Textured Buttons (CodePen style) */
    .ticket-button {
      position: relative;
      display: inline-block;
      padding: 1.5rem 3rem;
      font-family: "Prompt", system-ui, sans-serif;
      font-weight: 800;
      font-size: 1.5rem;
      text-transform: uppercase;
      letter-spacing: 0.1em;
      color: #fff;
      text-decoration: none;
      background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12px;
      box-shadow: 
        0 4px 15px 0 rgba(116, 79, 168, 0.75),
        inset 0 1px 0 rgba(255,255,255,0.2);
      transition: all 0.3s ease;
      cursor: pointer;
      overflow: hidden;
    }

    .ticket-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .ticket-button:hover {
      transform: translateY(-2px);
      box-shadow: 
        0 8px 25px 0 rgba(116, 79, 168, 0.9),
        inset 0 1px 0 rgba(255,255,255,0.3);
    }

    .ticket-button:hover::before {
      left: 100%;
    }

    .ticket-button:active {
      transform: translateY(0);
    }

    /* RGB Wall Style */
    .rgb-wall {
      isolation: isolate;
      min-height: 58vh;
      display: grid;
      place-items: center;
      gap: 2rem;
      padding: 8vh 4vw;
      background: #0f1114;
    }

    .rgb-hero {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: clamp(0.4rem, 2vw, 1.2rem);
      justify-content: center;
    }

    .rgb-plus, .rgb-eq {
      color: #8ea3b3;
      font-weight: 800;
      font-size: clamp(2rem, 8vw, 6rem);
    }

    .rgb-word {
      position: relative;
      display: inline-block;
      font-weight: 900;
      text-transform: uppercase;
      letter-spacing: -0.02em;
      line-height: 0.85;
      color: #eaeaea;
      font-size: clamp(3rem, 14vw, 12rem);
    }

    .rgb-word::before,
    .rgb-word::after {
      content: attr(data-txt);
      position: absolute;
      inset: 0;
      z-index: -1;
    }

    .rgb-word::before {
      transform: translate(0.18em, 0.06em);
      color: #e53935;
      opacity: 0.8;
    }

    .rgb-word::after {
      transform: translate(-0.14em, 0.08em);
      color: #03a9f4;
      opacity: 0.75;
    }

    .rgb-cta {
      display: inline-block;
      font-weight: 900;
      letter-spacing: 0.04em;
      padding: 0.9rem 1.25rem;
      border-radius: 12px;
      border: 1px solid #ffffff3a;
      background: #ffffff12;
      color: #fff;
      text-decoration: none;
      transition: background 0.2s ease;
    }

    .rgb-cta:hover {
      background: #ffffff1f;
    }

    /* Gallery Teaser */
    .gallery-teaser {
      padding: 4rem 2rem;
      background: var(--bg);
    }

    .gallery-teaser h2 {
      text-align: center;
      margin-bottom: 3rem;
      color: var(--fg);
      font-size: 2.5rem;
    }

    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
      max-width: 1200px;
      margin-left: auto;
      margin-right: auto;
    }

    .gallery-item {
      margin: 0;
      background: var(--card-bg);
      border: 1px solid var(--card-bd);
      border-radius: 16px;
      overflow: hidden;
      transition: transform 0.3s ease;
    }

    .gallery-item:hover {
      transform: translateY(-4px);
    }

    .gallery-item img {
      width: 100%;
      height: 250px;
      object-fit: cover;
      display: block;
    }

    .gallery-item figcaption {
      padding: 1rem;
      font-weight: 600;
      color: var(--name);
      text-align: center;
    }

    .gallery-cta {
      text-align: center;
    }

    .btn {
      display: inline-block;
      padding: 1rem 2rem;
      font-weight: 600;
      text-decoration: none;
      border-radius: 8px;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .btn-secondary {
      background: #333;
      color: #fff;
      border: 1px solid #555;
    }

    .btn-secondary:hover {
      background: #444;
    }

    /* Footer */
    .footer {
      background: #111;
      color: #ccc;
      padding: 3rem 2rem 1rem;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .footer-logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }

    .footer-tagline {
      color: #888;
      margin-bottom: 1rem;
    }

    .footer-section h3 {
      color: #fff;
      margin-bottom: 1rem;
    }

    .footer-section ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .footer-section li {
      margin-bottom: 0.5rem;
    }

    .footer-section a {
      color: #ccc;
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .footer-section a:hover {
      color: var(--pink);
    }

    .footer-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 2rem;
      border-top: 1px solid #333;
    }

    .footer-social {
      display: flex;
      gap: 1rem;
    }

    .footer-social a {
      font-size: 1.5rem;
      text-decoration: none;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
      .nav-container {
        padding: 0 1rem;
      }
      
      .nav-menu {
        gap: 1rem;
        font-size: 0.9rem;
      }

      .ticket-buttons {
        flex-direction: column;
        align-items: center;
        gap: 2rem;
      }

      .rgb-hero {
        flex-direction: column;
        text-align: center;
      }

      .gallery-grid {
        grid-template-columns: 1fr;
      }

      .footer-bottom {
        flex-direction: column;
        gap: 1rem;
      }
    }

    /* Reduced Motion */
    @media (prefers-reduced-motion: reduce) {
      .neon-line {
        animation: none;
      }
      
      .gallery-item {
        transition: none;
      }
      
      .gallery-item:hover {
        transform: none;
      }
      
      .ticket-button {
        transition: none;
      }
    }
  </style>
</head>

<body>
  <!-- Navigation -->
  <nav class="nav" role="navigation" aria-label="Main navigation">
    <div class="nav-container">
      <a href="#" class="nav-logo" aria-label="Chocolate & Art Show home">
        <img src="assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40">
        <span>Chocolate & Art</span>
      </a>

      <ul class="nav-menu" role="menubar">
        <li role="none"><a href="#" role="menuitem">Home</a></li>
        <li role="none"><a href="#" role="menuitem">Artists</a></li>
        <li role="none"><a href="#" role="menuitem">Tickets</a></li>
        <li role="none"><a href="#" role="menuitem">Gallery</a></li>
        <li role="none"><a href="#" role="menuitem">Schedule</a></li>
        <li role="none"><a href="#" role="menuitem">FAQ</a></li>
        <li role="none"><a href="#" role="menuitem">Contact</a></li>
      </ul>
    </div>
  </nav>

  <!-- Main Content -->
  <main>
    <!-- Neon Intro Section -->
    <section class="neon-intro" aria-label="Neon Sign Intro">
      <!-- Video Background -->
      <div class="video-background">
        <video id="chocoArtVideo" preload="auto" autoplay muted playsinline loop 
               poster="assets/images/hero-chocolate-art-tonight.png">
          <source src="assets/videos/chocoartshow.mp4" type="video/mp4">
          <img src="assets/images/hero-chocolate-art-tonight.png" 
               alt="Chocolate and Art Show fallback image" loading="lazy">
        </video>
      </div>

      <div class="neon-content">
        <div class="neon-line line-1" data-text="CHOCOLATE">CHOCOLATE</div>
        <div class="neon-line line-2" data-text="AND&nbsp;ART">AND ART</div>
        <div class="neon-line line-3" data-text="SHOW">SHOW</div>
      </div>
    </section>

    <!-- Ticket Buttons Section -->
    <section class="ticket-section">
      <div class="ticket-buttons">
        <div>
          <div class="buy-tickets-text" data-txt="BUY TICKETS THURSDAY">BUY TICKETS THURSDAY</div>
          <a href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089" 
             class="ticket-button" 
             rel="noopener noreferrer">
            September 18
          </a>
        </div>
        
        <div>
          <div class="buy-tickets-text" data-txt="BUY TICKETS FRIDAY">BUY TICKETS FRIDAY</div>
          <a href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089" 
             class="ticket-button" 
             rel="noopener noreferrer">
            September 19
          </a>
        </div>
      </div>
    </section>

    <!-- RGB Wall -->
    <section class="rgb-wall" aria-label="Art + Music + Chocolate = Dallas">
      <div class="rgb-hero">
        <span class="rgb-word" data-txt="ART">ART</span>
        <span class="rgb-plus">+</span>
        <span class="rgb-word" data-txt="MUSIC">MUSIC</span>
        <span class="rgb-plus">+</span>
        <span class="rgb-word" data-txt="CHOCOLATE">CHOCOLATE</span>
        <span class="rgb-eq">=</span>
        <span class="rgb-word" data-txt="DALLAS">DALLAS</span>
      </div>
    </section>

    <!-- Gallery Teaser -->
    <section class="gallery-teaser" aria-label="Gallery preview">
      <div class="container">
        <h2>Experience the Art</h2>
        <div class="gallery-grid">
          <figure class="gallery-item">
            <img src="https://picsum.photos/400/300?random=1" alt="Live art creation" loading="lazy" width="400" height="300">
            <figcaption>Live Art Creation</figcaption>
          </figure>
          <figure class="gallery-item">
            <img src="https://picsum.photos/400/300?random=2" alt="Body art performance" loading="lazy" width="400" height="300">
            <figcaption>Body Art</figcaption>
          </figure>
          <figure class="gallery-item">
            <img src="https://picsum.photos/400/300?random=3" alt="Live music performance" loading="lazy" width="400" height="300">
            <figcaption>Live Music</figcaption>
          </figure>
          <figure class="gallery-item">
            <img src="https://picsum.photos/400/300?random=4" alt="Artisan chocolate display" loading="lazy" width="400" height="300">
            <figcaption>Artisan Chocolate</figcaption>
          </figure>
          <figure class="gallery-item">
            <img src="https://picsum.photos/400/300?random=5" alt="Interactive art experience" loading="lazy" width="400" height="300">
            <figcaption>Interactive Experience</figcaption>
          </figure>
          <figure class="gallery-item">
            <img src="https://picsum.photos/400/300?random=6" alt="Night atmosphere" loading="lazy" width="400" height="300">
            <figcaption>Night Atmosphere</figcaption>
          </figure>
        </div>
        <div class="gallery-cta">
          <a href="#" class="btn btn-secondary">View Full Gallery</a>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <div class="footer-logo">
            <img src="assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40">
            <span>Chocolate & Art Show</span>
          </div>
          <p class="footer-tagline">Where art meets chocolate, and creativity flows.</p>
        </div>

        <div class="footer-section">
          <h3>Event Info</h3>
          <ul>
            <li>Thursday & Friday, September 18-19, 2025</li>
            <li>Lofty Spaces, Dallas</li>
            <li>21+ Event</li>
            <li>Doors: 7:00 PM</li>
          </ul>
        </div>

        <div class="footer-section">
          <h3>Quick Links</h3>
          <ul>
            <li><a href="#">Buy Tickets</a></li>
            <li><a href="#">Meet Artists</a></li>
            <li><a href="#">Event Schedule</a></li>
            <li><a href="#">Photo Gallery</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h3>Get Involved</h3>
          <ul>
            <li><a href="mailto:<EMAIL>?subject=Artist Application">Apply as Artist</a></li>
            <li><a href="mailto:<EMAIL>?subject=Vendor Application">Apply as Vendor</a></li>
            <li><a href="#">Contact Us</a></li>
          </ul>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="footer-legal">
          <p>&copy; 2025 Chocolate & Art Show. All rights reserved.</p>
          <p>An immersive experience celebrating art, music, and artisan chocolate.</p>
        </div>
        <div class="footer-social">
          <a href="mailto:<EMAIL>" aria-label="Email us">📧</a>
          <a href="#" aria-label="Interactive playground">🎨</a>
        </div>
      </div>
    </div>
  </footer>

  <script>
    // Video handling
    const vid = document.getElementById("chocoArtVideo");
    if (vid) {
      vid.addEventListener("loadedmetadata", () => {
        if (vid.duration > 33) {
          vid.currentTime = 33;
        }
      });
    }
  </script>
</body>

</html>