<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Contact — Chocolate & Art Show Dallas | September 18-19, 2025</title>
  <meta name="description"
    content="Contact Chocolate & Art Show Dallas. Get directions to Lofty Spaces, parking information, and reach out with questions.">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Font loading -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap" rel="stylesheet">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="assets/images/choco-logo.png">

  <!-- Canonical URL -->
  <link rel="canonical" href="https://chocolateandartshow.com/contact.html">

  <!-- Styles -->
  <link rel="stylesheet" href="assets/css/global.css">
  <link rel="stylesheet" href="assets/css/components.css">
</head>

<body>
  <!-- Navigation -->
  <nav class="nav" role="navigation" aria-label="Main navigation">
    <div class="nav-container">
      <a href="index.html" class="nav-logo" aria-label="Chocolate & Art Show home">
        <img src="assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40">
        <span>Chocolate & Art</span>
      </a>

      <ul class="nav-menu" role="menubar">
        <li role="none"><a href="index.html" role="menuitem">Home</a></li>
        <li role="none"><a href="artists/index.html" role="menuitem">Artists</a></li>
        <li role="none"><a href="tickets.html" role="menuitem">Tickets</a></li>
        <li role="none"><a href="gallery.html" role="menuitem">Gallery</a></li>
        <li role="none"><a href="schedule.html" role="menuitem">Schedule</a></li>
        <li role="none"><a href="faq.html" role="menuitem">FAQ</a></li>
        <li role="none"><a href="contact.html" role="menuitem" aria-current="page">Contact</a></li>
      </ul>
    </div>
  </nav>

  <!-- Main Content -->
  <main id="main-content">
    <div class="container">
      <section class="contact-header">
        <h1>Contact & Venue Information</h1>
        <p>Everything you need to know about getting to Chocolate & Art Show Dallas</p>
      </section>

      <div class="contact-grid">
        <!-- Venue Information -->
        <section class="venue-info">
          <h2>📍 Venue</h2>
          <div class="venue-card">
            <h3>Lofty Spaces</h3>
            <address>
              Dallas, TX<br>
              United States
            </address>
            <div class="venue-details">
              <p><strong>Event Dates:</strong> September 18-19, 2025</p>
              <p><strong>Doors Open:</strong> 7:00 PM</p>
              <p><strong>Last Entry:</strong> 12:30 AM</p>
              <p><strong>Age Requirement:</strong> 21+ (ID Required)</p>
            </div>
          </div>
        </section>

        <!-- Contact Information -->
        <section class="contact-info">
          <h2>📧 Get in Touch</h2>
          <div class="contact-methods">
            <div class="contact-method">
              <h3>General Inquiries</h3>
              <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </div>
        </section>
      </div>

      <!-- Transportation -->
      <section class="transportation">
        <h2>🚗 Getting There</h2>
        <div class="transport-grid">
          <div class="transport-option">
            <h3>Driving</h3>
            <p>Parking is available at the venue. We recommend arriving early as spaces are limited.</p>
          </div>

          <div class="transport-option">
            <h3>Rideshare</h3>
            <p>Uber and Lyft are recommended for convenience. The venue is easily accessible for pickup and drop-off.
            </p>
          </div>

          <div class="transport-option">
            <h3>Public Transit</h3>
            <p>Check local Dallas transit options for routes to the venue area.</p>
          </div>
        </div>
      </section>

      <!-- Important Notes -->
      <section class="important-notes">
        <h2>⚠️ Important Information</h2>
        <div class="notes-grid">
          <div class="note-card">
            <h3>ID Required</h3>
            <p>This is a 21+ event. Valid government-issued photo ID is required for entry. No exceptions.</p>
          </div>

          <div class="note-card">
            <h3>No Re-entry</h3>
            <p>Once you leave the venue, re-entry is not permitted. Please plan accordingly.</p>
          </div>

          <div class="note-card">
            <h3>Capacity Limited</h3>
            <p>This is an intimate event with limited capacity. Advance ticket purchase is strongly recommended.</p>
          </div>

          <div class="note-card">
            <h3>Dress Code</h3>
            <p>Come as you are! We encourage creative expression and comfortable attire for an evening of art and music.
            </p>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="contact-cta">
        <h2>Ready to Join Us?</h2>
        <p>Secure your spot at Dallas's most immersive art experience.</p>
        <div class="cta-buttons">
          <a href="tickets.html" class="btn btn-primary">Get Tickets</a>
          <a href="artists/index.html" class="btn btn-secondary">Apply to Participate</a>
        </div>
      </section>
    </div>
  </main>

  <!-- Scripts -->
  <script src="assets/js/global.js"></script>

  <style>
    .contact-header {
      text-align: center;
      padding: 4rem 0 2rem;
    }

    .contact-header h1 {
      margin-bottom: 1rem;
      color: var(--fg);
    }

    .contact-header p {
      color: var(--muted);
      font-size: 1.1rem;
    }

    .contact-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 3rem;
      margin: 3rem 0;
    }

    .venue-info h2,
    .contact-info h2 {
      margin-bottom: 2rem;
      color: var(--fg);
      font-size: 1.5rem;
    }

    .venue-card {
      background: var(--card-bg);
      border: 1px solid var(--card-bd);
      border-radius: 16px;
      padding: 2rem;
    }

    .venue-card h3 {
      margin-bottom: 1rem;
      color: var(--name);
      font-size: 1.5rem;
    }

    .venue-card address {
      font-style: normal;
      color: var(--muted);
      margin-bottom: 1.5rem;
      line-height: 1.6;
    }

    .venue-details p {
      margin-bottom: 0.5rem;
      color: var(--muted);
    }

    .contact-methods {
      display: grid;
      gap: 1.5rem;
    }

    .contact-method {
      background: var(--card-bg);
      border: 1px solid var(--card-bd);
      border-radius: 12px;
      padding: 1.5rem;
    }

    .contact-method h3 {
      margin-bottom: 0.5rem;
      color: var(--name);
      font-size: 1.1rem;
    }

    .contact-method a {
      color: #a0c2ff;
      text-decoration: none;
    }

    .contact-method a:hover {
      text-decoration: underline;
    }

    .transportation,
    .important-notes {
      margin: 4rem 0;
    }

    .transportation h2,
    .important-notes h2 {
      text-align: center;
      margin-bottom: 3rem;
      color: var(--fg);
      font-size: 1.75rem;
    }

    .transport-grid,
    .notes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
    }

    .transport-option,
    .note-card {
      background: var(--card-bg);
      border: 1px solid var(--card-bd);
      border-radius: 16px;
      padding: 2rem;
      text-align: center;
    }

    .transport-option h3,
    .note-card h3 {
      margin-bottom: 1rem;
      color: var(--name);
      font-size: 1.25rem;
    }

    .transport-option p,
    .note-card p {
      color: var(--muted);
      line-height: 1.6;
      margin: 0;
    }

    .contact-cta {
      text-align: center;
      padding: 4rem 0;
      margin-top: 3rem;
      border-top: 1px solid var(--card-bd);
    }

    .contact-cta h2 {
      margin-bottom: 1rem;
      color: var(--fg);
    }

    .contact-cta p {
      margin-bottom: 2rem;
      color: var(--muted);
      font-size: 1.1rem;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    @media (max-width: 768px) {
      .contact-header {
        padding: 2rem 0 1rem;
      }

      .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin: 2rem 0;
      }

      .venue-card,
      .contact-method,
      .transport-option,
      .note-card {
        padding: 1.5rem;
      }

      .transport-grid,
      .notes-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .cta-buttons {
        flex-direction: column;
        align-items: center;
      }

      .cta-buttons .btn {
        width: 100%;
        max-width: 250px;
      }
    }
  </style>
</body>

</html>