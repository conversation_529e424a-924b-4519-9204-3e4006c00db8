<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Artists — Chocolate & Art Show Dallas | September 18-19, 2025</title>
  <meta name="description"
    content="Meet the featured artists at Chocolate & Art Show Dallas. Local and emerging artists showcasing their work alongside live music and artisan chocolate.">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Font loading -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@800&display=swap" rel="stylesheet">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="../assets/images/choco-logo.png">

  <!-- Open Graph / Social Media -->
  <meta property="og:title" content="Artists — Chocolate & Art Show Dallas">
  <meta property="og:description"
    content="Meet the featured artists at Chocolate & Art Show Dallas. Local and emerging artists showcasing their work.">
  <meta property="og:image" content="/assets/images/og-artists.jpg">
  <meta property="og:url" content="https://chocolateandartshow.com/artists">

  <!-- Canonical URL -->
  <link rel="canonical" href="https://chocolateandartshow.com/artists">

  <!-- Styles -->
  <link rel="stylesheet" href="../assets/css/global.css">
  <link rel="stylesheet" href="../assets/css/components.css">
  <link rel="stylesheet" href="../assets/css/artists.css">
</head>

<body>
  <!-- Skip to main content for accessibility -->
  <a href="#main-content" class="sr-only">Skip to main content</a>

  <!-- Navigation -->
  <nav class="nav" role="navigation" aria-label="Main navigation">
    <div class="nav-container">
      <a href="../index.html" class="nav-logo" aria-label="Chocolate & Art Show home">
        <img src="../assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40">
        <span>Chocolate & Art</span>
      </a>

      <ul class="nav-menu" role="menubar">
        <li role="none">
          <a href="../index.html" role="menuitem">Home</a>
        </li>
        <li role="none">
          <a href="index.html" role="menuitem" aria-current="page">Artists</a>
        </li>
        <li role="none">
          <a href="../tickets.html" role="menuitem">Tickets</a>
        </li>
        <li role="none">
          <a href="../gallery.html" role="menuitem">Gallery</a>
        </li>
        <li role="none">
          <a href="../schedule.html" role="menuitem">Schedule</a>
        </li>
        <li role="none">
          <a href="../faq.html" role="menuitem">FAQ</a>
        </li>
        <li role="none">
          <a href="../contact.html" role="menuitem">Contact</a>
        </li>
      </ul>
    </div>
  </nav>

  <!-- Main Content -->
  <main id="main-content">
    <!-- Artists Header with Trippy Refresh Background -->
    <header class="artists-hero">
      <h1>Artists</h1>
    </header>

    <div class="container">
      <!-- Apply CTA Section -->
      <section class="apply-cta" aria-label="Apply to Chocolate & Art Show — Dallas">
        <div class="apply-inner">
          <h2>Artists • Vendors — Apply</h2>
          <p>Spots are limited. Dallas is almost sold out — email us to be considered.</p>
          <div class="btns">
            <a class="btn" data-gmail to="<EMAIL>"
              data-subject="Dallas Artist Application — Sept 18–19"
              data-body="Name:%0D%0ALinks:%0D%0AMedium:%0D%0A">Artists apply</a>
            <a class="btn" data-gmail to="<EMAIL>"
              data-subject="Dallas Vendor Application — Sept 18–19"
              data-body="Name:%0D%0AProduct:%0D%0ALinks:%0D%0A">Vendors apply</a>
          </div>
        </div>
      </section>

      <!-- Featured Artists Grid -->
      <section class="artist-preview" aria-label="Featured artists — Dallas">
        <!-- SVG clip-path defs (once per page) -->
        <svg class="visually-hidden" aria-hidden="true" width="0" height="0" focusable="false">
          <defs>
            <clipPath id="blob-1" clipPathUnits="objectBoundingBox">
              <path
                d="M0.02,0.42 C0.05,0.22 0.20,0.06 0.40,0.06 L0.88,0.06 C0.96,0.06 0.99,0.16 0.98,0.30 C0.96,0.46 0.86,0.66 0.66,0.70 C0.49,0.74 0.40,0.67 0.27,0.72 C0.15,0.76 0.05,0.68 0.03,0.55 Z" />
            </clipPath>
            <clipPath id="blob-2" clipPathUnits="objectBoundingBox">
              <path
                d="M0.03,0.38 C0.09,0.16 0.23,0.06 0.45,0.06 L0.86,0.06 C0.96,0.06 1.00,0.18 0.98,0.34 C0.96,0.50 0.87,0.69 0.68,0.73 C0.50,0.77 0.38,0.68 0.24,0.74 C0.12,0.78 0.01,0.70 0.02,0.55 Z" />
            </clipPath>
            <clipPath id="blob-3" clipPathUnits="objectBoundingBox">
              <path
                d="M0.04,0.40 C0.07,0.20 0.22,0.07 0.42,0.07 L0.85,0.07 C0.96,0.07 1.00,0.20 0.98,0.35 C0.95,0.52 0.85,0.70 0.64,0.73 C0.44,0.77 0.34,0.67 0.20,0.73 C0.09,0.77 0.01,0.68 0.02,0.53 Z" />
            </clipPath>
            <clipPath id="blob-4" clipPathUnits="objectBoundingBox">
              <path
                d="M0.02,0.36 C0.06,0.17 0.21,0.05 0.39,0.05 L0.86,0.05 C0.96,0.05 1.00,0.17 0.98,0.32 C0.95,0.49 0.85,0.69 0.65,0.73 C0.47,0.76 0.36,0.68 0.22,0.74 C0.10,0.78 0.02,0.70 0.02,0.56 Z" />
            </clipPath>
            <clipPath id="blob-5" clipPathUnits="objectBoundingBox">
              <path
                d="M0.03,0.39 C0.08,0.18 0.23,0.06 0.41,0.06 L0.87,0.06 C0.97,0.06 1.00,0.17 0.98,0.33 C0.96,0.49 0.86,0.68 0.67,0.72 C0.49,0.76 0.39,0.68 0.26,0.73 C0.14,0.77 0.04,0.70 0.03,0.56 Z" />
            </clipPath>
          </defs>
        </svg>

        <ul class="artist-grid" role="list">
          <li class="artist-card">
            <a class="artist-link" href="danica.html" aria-label="View Danica — bio and works">
              <figure class="media blob-1">
                <img src="../assets/artists/danica/thumb.jpg" alt="Danica — portrait" loading="lazy" width="640"
                  height="420">
              </figure>
              <span class="name">Danica</span>
            </a>
          </li>

          <li class="artist-card">
            <a class="artist-link" href="david-v.html" aria-label="View David V — bio and works">
              <figure class="media blob-2">
                <img src="../assets/artists/david-v/thumb.jpg" alt="David V — portrait with hat" loading="lazy"
                  width="640" height="420">
              </figure>
              <span class="name">David V.</span>
            </a>
          </li>

          <li class="artist-card">
            <a class="artist-link" href="omi.html" aria-label="View Omi — bio and works">
              <figure class="media blob-3">
                <img src="../assets/artists/omi/thumb.jpg" alt="Omi — profile with sunglasses" loading="lazy"
                  width="640" height="420">
              </figure>
              <span class="name">Omi</span>
            </a>
          </li>

          <li class="artist-card">
            <a class="artist-link" href="sofia.html" aria-label="View Sofía — bio and works">
              <figure class="media blob-4">
                <img src="../assets/artists/sofia/thumb.jpg" alt="Sofía — portrait with hat" loading="lazy" width="640"
                  height="420">
              </figure>
              <span class="name">Sofía</span>
            </a>
          </li>

          <li class="artist-card">
            <a class="artist-link" href="leon.html" aria-label="View León — bio and works">
              <figure class="media blob-5">
                <img src="../assets/artists/leon/thumb.jpg" alt="León — studio portrait" loading="lazy" width="640"
                  height="420">
              </figure>
              <span class="name">León</span>
            </a>
          </li>
        </ul>
      </section>

      <!-- Kusama Playground Teaser -->
      <a class="kusama-card" href="../play/kusama/index.html" aria-label="Open the Kusama dot playground">
        <figure><img src="../assets/play/kusama-thumb.jpg" alt="Preview of Kusama dot playground" loading="lazy"
            width="1280" height="720"></figure>
        <span>Play with the dots →</span>
      </a>
    </div>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <div class="footer-logo">
            <img src="../assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40">
            <span>Chocolate & Art Show</span>
          </div>
          <p class="footer-tagline">Where art meets chocolate, and creativity flows.</p>
        </div>

        <div class="footer-section">
          <h3>Event Info</h3>
          <ul>
            <li>September 18-19, 2025</li>
            <li>Lofty Spaces, Dallas</li>
            <li>21+ Event</li>
            <li>Doors: 7:00 PM</li>
          </ul>
        </div>

        <div class="footer-section">
          <h3>Quick Links</h3>
          <ul>
            <li><a href="../tickets.html">Buy Tickets</a></li>
            <li><a href="index.html">Meet Artists</a></li>
            <li><a href="../schedule.html">Event Schedule</a></li>
            <li><a href="../gallery.html">Photo Gallery</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h3>Get Involved</h3>
          <ul>
            <li><a href="mailto:<EMAIL>?subject=Artist Application">Apply as Artist</a></li>
            <li><a href="mailto:<EMAIL>?subject=Vendor Application">Apply as Vendor</a></li>
            <li><a href="../contact.html">Contact Us</a></li>
          </ul>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="footer-legal">
          <p>&copy; 2025 Chocolate & Art Show. All rights reserved.</p>
          <p>An immersive experience celebrating art, music, and artisan chocolate.</p>
        </div>
        <div class="footer-social">
          <a href="mailto:<EMAIL>" aria-label="Email us">
            <span>📧</span>
          </a>
          <a href="../play/kusama/index.html" aria-label="Interactive playground">
            <span>🎨</span>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="../assets/js/global.js"></script>

  <!-- Optional: randomize hue/angle on each refresh -->
  <script>
    document.documentElement.style.setProperty('--h', Math.floor(Math.random() * 360));
    document.documentElement.style.setProperty('--a', Math.floor(Math.random() * 360) + 'deg');
  </script>
</body>

</html>