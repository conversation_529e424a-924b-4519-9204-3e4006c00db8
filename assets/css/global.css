/* Chocolate & Art Show - Global Styles */

/* CSS Variables */
:root {
  /* Colors */
  --bg: #0b0b0d;
  --fg: #ececec;
  --muted: #b6beca;
  --card-bg: #101113;
  --card-bd: #ffffff1a;
  --name: #f2f2f2;

  /* Noise texture overlay */
  --noise: url("data:image/svg+xml,%3Csvg viewBox='0 0 250 250' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='6.29' numOctaves='6' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  background: var(--bg);
  color: var(--fg);
  font: 400 16px/1.55 Inter, system-ui, "Segoe UI", Roboto, Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Global textured background overlay */
body::after {
  content: "";
  position: fixed;
  inset: 0;
  pointer-events: none;
  background: var(--noise);
  opacity: 0.38;
  mix-blend-mode: overlay;
  z-index: -1;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: 800;
  line-height: 1.2;
}

h1 {
  font-size: clamp(2rem, 6vw, 4rem);
}

h2 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
}

h3 {
  font-size: clamp(1.25rem, 3vw, 1.75rem);
}

p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

a {
  color: #a0c2ff;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Layout Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.wrap {
  max-width: 1100px;
  margin: 0 auto;
  padding: 26px;
}

/* Accessibility */
.visually-hidden {
  position: absolute !important;
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  width: 1px;
  height: 1px;
  overflow: hidden;
  white-space: nowrap;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Management */
*:focus-visible {
  outline: 3px solid #fff3;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Button Base Styles */
.btn {
  display: inline-block;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  font-weight: 800;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  line-height: 1;
}

.btn:focus-visible {
  outline: 3px solid #fff3;
  outline-offset: 2px;
}

.btn-primary {
  background: #1d66ff;
  color: #fff;
}

.btn-primary:hover {
  background: #0052e6;
  text-decoration: none;
}

.btn-secondary {
  background: #ffffff12;
  color: #fff;
  border: 1px solid #ffffff33;
}

.btn-secondary:hover {
  background: #ffffff1f;
  text-decoration: none;
}

/* Navigation */
.nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(11, 11, 13, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--card-bd);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-logo {
  display: flex;
  align-items: center;
  font-weight: 800;
  font-size: 1.25rem;
  color: var(--fg);
  text-decoration: none;
}

.nav-logo img {
  height: 40px;
  width: auto;
  margin-right: 0.5rem;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-menu a {
  color: var(--fg);
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s ease;
}

.nav-menu a:hover {
  color: #a0c2ff;
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .nav-container {
    padding: 1rem;
  }

  .nav-menu {
    gap: 1rem;
    font-size: 0.9rem;
  }
}

/* Main Content Spacing */
main {
  margin-top: 80px;
  /* Account for fixed nav */
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Footer */
.footer {
  background: #080809;
  border-top: 1px solid var(--card-bd);
  margin-top: 4rem;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  color: var(--fg);
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 0.5rem;
  color: var(--muted);
  font-size: 0.9rem;
}

.footer-section a {
  color: var(--muted);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-section a:hover {
  color: #a0c2ff;
  text-decoration: underline;
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-weight: 700;
  color: var(--fg);
}

.footer-logo img {
  margin-right: 0.5rem;
}

.footer-tagline {
  color: var(--muted);
  font-style: italic;
  margin: 0;
  font-size: 0.9rem;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid var(--card-bd);
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-legal p {
  margin: 0;
  color: var(--muted);
  font-size: 0.85rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--card-bg);
  border: 1px solid var(--card-bd);
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.2s ease;
}

.footer-social a:hover {
  background: #ffffff12;
  transform: translateY(-2px);
}

.footer-social span {
  font-size: 1.2rem;
}

/* Mobile Footer */
@media (max-width: 768px) {
  .footer {
    padding: 2rem 0 1rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .footer-legal {
    order: 2;
  }

  .footer-social {
    order: 1;
  }
}

/* Print Styles */
@media print {
  body::after {
    display: none;
  }

  .nav {
    position: static;
    background: transparent;
    border: none;
  }

  main {
    margin-top: 0;
  }

  .footer {
    display: none;
  }
}