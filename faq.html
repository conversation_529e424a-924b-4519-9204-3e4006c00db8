<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>FAQ — Chocolate & Art Show Dallas | September 18-19, 2025</title>
  <meta name="description" content="Frequently asked questions about Chocolate & Art Show Dallas. Get answers about tickets, venue, age requirements, and more.">
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- Font loading -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap" rel="stylesheet">
  
  <!-- Favicon -->
  <link rel="icon" type="image/png" href="assets/images/choco-logo.png">
  
  <!-- Canonical URL -->
  <link rel="canonical" href="https://chocolateandartshow.com/faq.html">
  
  <!-- Styles -->
  <link rel="stylesheet" href="assets/css/global.css">
  <link rel="stylesheet" href="assets/css/components.css">
</head>
<body>
  <!-- Navigation -->
  <nav class="nav" role="navigation" aria-label="Main navigation">
    <div class="nav-container">
      <a href="index.html" class="nav-logo" aria-label="Chocolate & Art Show home">
        <img src="assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40">
        <span>Chocolate & Art</span>
      </a>
      
      <ul class="nav-menu" role="menubar">
        <li role="none"><a href="index.html" role="menuitem">Home</a></li>
        <li role="none"><a href="artists/index.html" role="menuitem">Artists</a></li>
        <li role="none"><a href="tickets.html" role="menuitem">Tickets</a></li>
        <li role="none"><a href="gallery.html" role="menuitem">Gallery</a></li>
        <li role="none"><a href="schedule.html" role="menuitem">Schedule</a></li>
        <li role="none"><a href="faq.html" role="menuitem" aria-current="page">FAQ</a></li>
        <li role="none"><a href="contact.html" role="menuitem">Contact</a></li>
      </ul>
    </div>
  </nav>

  <!-- Main Content -->
  <main id="main-content">
    <div class="container">
      <section class="faq-header">
        <h1>Frequently Asked Questions</h1>
        <p>Everything you need to know about Chocolate & Art Show Dallas</p>
      </section>

      <section class="faq-content">
        <div class="faq-item">
          <button class="faq-question" aria-expanded="false">
            <span>What is Chocolate & Art Show?</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer">
            <p>Chocolate & Art Show is an immersive experience combining live art creation, music performances, body art, and artisan chocolate tasting. It's a unique cultural event that brings together local artists, musicians, and chocolatiers for two unforgettable nights.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" aria-expanded="false">
            <span>When and where is the event?</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer">
            <p>The event takes place on September 18-19, 2025, at Lofty Spaces in Dallas, TX. Doors open at 7:00 PM both nights, with the last entry at 12:30 AM.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" aria-expanded="false">
            <span>Is this a 21+ event?</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer">
            <p>Yes, this is a 21+ event. Valid government-issued photo ID is required for entry. No exceptions.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" aria-expanded="false">
            <span>How much are tickets?</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer">
            <p>Ticket prices vary by night and availability. Visit our <a href="tickets.html">tickets page</a> for current pricing and to purchase tickets through Eventbrite.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" aria-expanded="false">
            <span>What should I expect at the event?</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer">
            <p>You'll experience live art creation, interactive installations, body art performances, live music and DJ sets, artisan chocolate tastings, and an immersive atmosphere that blends all these elements together.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" aria-expanded="false">
            <span>Can I purchase tickets at the door?</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer">
            <p>Due to limited capacity, we strongly recommend purchasing tickets in advance. Door sales are not guaranteed and depend on availability.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" aria-expanded="false">
            <span>Is there parking available?</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer">
            <p>Yes, there is parking available at the venue. We also recommend rideshare services for convenience. See our <a href="contact.html">contact page</a> for detailed directions and parking information.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" aria-expanded="false">
            <span>Can I bring a camera?</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer">
            <p>Personal photography is welcome! We encourage sharing your experience on social media. Professional photography equipment may require prior approval.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" aria-expanded="false">
            <span>How can I apply to participate as an artist?</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer">
            <p>Visit our <a href="artists/index.html">artists page</a> to apply as an artist, vendor, or musician. Space is limited and applications are reviewed on a rolling basis.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" aria-expanded="false">
            <span>What if I have other questions?</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer">
            <p>For additional questions, please email us at <a href="mailto:<EMAIL>"><EMAIL></a> or visit our <a href="contact.html">contact page</a>.</p>
          </div>
        </div>
      </section>

      <section class="faq-cta">
        <h2>Ready to Experience It?</h2>
        <p>Join us for two nights of art, music, and chocolate in Dallas.</p>
        <a href="tickets.html" class="btn btn-primary">Get Your Tickets</a>
      </section>
    </div>
  </main>

  <!-- Scripts -->
  <script src="assets/js/global.js"></script>
  <script>
    // Simple FAQ accordion functionality
    document.addEventListener('DOMContentLoaded', function() {
      const faqItems = document.querySelectorAll('.faq-item');
      
      faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        const icon = item.querySelector('.faq-icon');
        
        question.addEventListener('click', () => {
          const isExpanded = question.getAttribute('aria-expanded') === 'true';
          
          // Close all other items
          faqItems.forEach(otherItem => {
            const otherQuestion = otherItem.querySelector('.faq-question');
            const otherAnswer = otherItem.querySelector('.faq-answer');
            const otherIcon = otherItem.querySelector('.faq-icon');
            
            otherQuestion.setAttribute('aria-expanded', 'false');
            otherAnswer.style.maxHeight = '0';
            otherIcon.textContent = '+';
          });
          
          // Toggle current item
          if (!isExpanded) {
            question.setAttribute('aria-expanded', 'true');
            answer.style.maxHeight = answer.scrollHeight + 'px';
            icon.textContent = '−';
          }
        });
      });
    });
  </script>

  <style>
    .faq-header {
      text-align: center;
      padding: 4rem 0 2rem;
    }
    
    .faq-header h1 {
      margin-bottom: 1rem;
      color: var(--fg);
    }
    
    .faq-header p {
      color: var(--muted);
      font-size: 1.1rem;
    }
    
    .faq-content {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem 0;
    }
    
    .faq-item {
      border: 1px solid var(--card-bd);
      border-radius: 12px;
      margin-bottom: 1rem;
      overflow: hidden;
    }
    
    .faq-question {
      width: 100%;
      padding: 1.5rem;
      background: var(--card-bg);
      border: none;
      color: var(--fg);
      font-size: 1.1rem;
      font-weight: 600;
      text-align: left;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: background 0.2s ease;
    }
    
    .faq-question:hover {
      background: #ffffff08;
    }
    
    .faq-question:focus-visible {
      outline: 3px solid #fff3;
      outline-offset: -3px;
    }
    
    .faq-icon {
      font-size: 1.5rem;
      font-weight: 300;
      color: var(--muted);
    }
    
    .faq-answer {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
      background: var(--bg);
    }
    
    .faq-answer p {
      padding: 1.5rem;
      margin: 0;
      color: var(--muted);
      line-height: 1.6;
    }
    
    .faq-answer a {
      color: #a0c2ff;
    }
    
    .faq-cta {
      text-align: center;
      padding: 4rem 0;
      margin-top: 3rem;
      border-top: 1px solid var(--card-bd);
    }
    
    .faq-cta h2 {
      margin-bottom: 1rem;
      color: var(--fg);
    }
    
    .faq-cta p {
      margin-bottom: 2rem;
      color: var(--muted);
    }
    
    @media (max-width: 768px) {
      .faq-header {
        padding: 2rem 0 1rem;
      }
      
      .faq-question {
        padding: 1rem;
        font-size: 1rem;
      }
      
      .faq-answer p {
        padding: 1rem;
      }
      
      .faq-cta {
        padding: 2rem 0;
      }
    }
  </style>
</body>
</html>
