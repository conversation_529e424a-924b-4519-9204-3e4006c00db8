#!/bin/bash

# Fix relative paths for local development
echo "🔧 Fixing asset paths for local development..."

# Fix root level pages (tickets.html, gallery.html, etc.)
for file in tickets.html gallery.html schedule.html faq.html contact.html; do
    if [ -f "$file" ]; then
        echo "  Fixing $file..."
        # Fix CSS links
        sed -i '' 's|href="/assets/css/|href="assets/css/|g' "$file"
        # Fix favicon
        sed -i '' 's|href="/assets/images/choco-logo.png"|href="assets/images/choco-logo.png"|g' "$file"
        # Fix logo in navigation
        sed -i '' 's|src="/assets/images/choco-logo.png"|src="assets/images/choco-logo.png"|g' "$file"
        # Fix navigation links
        sed -i '' 's|href="/"|href="index.html"|g' "$file"
        sed -i '' 's|href="/artists"|href="artists/index.html"|g' "$file"
        sed -i '' 's|href="/tickets.html"|href="tickets.html"|g' "$file"
        sed -i '' 's|href="/gallery.html"|href="gallery.html"|g' "$file"
        sed -i '' 's|href="/schedule.html"|href="schedule.html"|g' "$file"
        sed -i '' 's|href="/faq.html"|href="faq.html"|g' "$file"
        sed -i '' 's|href="/contact.html"|href="contact.html"|g' "$file"
        # Fix JavaScript
        sed -i '' 's|src="/assets/js/|src="assets/js/|g' "$file"
        # Fix gallery images
        sed -i '' 's|src="/assets/gallery/|src="assets/gallery/|g' "$file"
    fi
done

# Fix artist detail pages
for file in artists/*.html; do
    if [ -f "$file" ] && [ "$(basename "$file")" != "index.html" ]; then
        echo "  Fixing $file..."
        # Fix CSS links (need ../ for subdirectory)
        sed -i '' 's|href="/assets/css/|href="../assets/css/|g' "$file"
        # Fix favicon
        sed -i '' 's|href="/assets/images/choco-logo.png"|href="../assets/images/choco-logo.png"|g' "$file"
        # Fix logo in navigation
        sed -i '' 's|src="/assets/images/choco-logo.png"|src="../assets/images/choco-logo.png"|g' "$file"
        # Fix navigation links
        sed -i '' 's|href="/"|href="../index.html"|g' "$file"
        sed -i '' 's|href="/artists"|href="index.html"|g' "$file"
        sed -i '' 's|href="/tickets.html"|href="../tickets.html"|g' "$file"
        sed -i '' 's|href="/gallery.html"|href="../gallery.html"|g' "$file"
        sed -i '' 's|href="/schedule.html"|href="../schedule.html"|g' "$file"
        sed -i '' 's|href="/faq.html"|href="../faq.html"|g' "$file"
        sed -i '' 's|href="/contact.html"|href="../contact.html"|g' "$file"
        # Fix artist images
        sed -i '' 's|src="/assets/artists/|src="../assets/artists/|g' "$file"
        # Fix back link
        sed -i '' 's|href="/artists"|href="index.html"|g' "$file"
    fi
done

# Fix Kusama playground
if [ -f "play/kusama/index.html" ]; then
    echo "  Fixing play/kusama/index.html..."
    # Fix favicon
    sed -i '' 's|href="/assets/images/choco-logo.png"|href="../../assets/images/choco-logo.png"|g' "play/kusama/index.html"
    # Fix back link
    sed -i '' 's|href="/artists"|href="../../artists/index.html"|g' "play/kusama/index.html"
fi

echo "✅ Path fixing complete!"
echo ""
echo "📝 Note: These changes are for local development only."
echo "   When deploying to a web server, use absolute paths starting with /"
EOF
