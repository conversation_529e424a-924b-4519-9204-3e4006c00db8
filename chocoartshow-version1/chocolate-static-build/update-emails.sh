#!/bin/bash

# Update all email <NAME_EMAIL>
echo "📧 Updating all email <NAME_EMAIL>..."

# Update all HTML files
for file in *.html artists/*.html play/kusama/*.html; do
    if [ -f "$file" ]; then
        echo "  Updating $file..."
        # Replace all instances of the old email
        sed -i '' 's/ChocolateandArt@gmail\.com/<EMAIL>/g' "$file"
    fi
done

# Update JavaScript files
for file in assets/js/*.js; do
    if [ -f "$file" ]; then
        echo "  Updating $file..."
        sed -i '' 's/ChocolateandArt@gmail\.com/<EMAIL>/g' "$file"
    fi
done

# Update README if it exists
if [ -f "README.md" ]; then
    echo "  Updating README.md..."
    sed -i '' 's/ChocolateandArt@gmail\.com/<EMAIL>/g' "README.md"
fi

echo "✅ Email addresses updated!"
echo ""
echo "📋 Summary of changes:"
echo "- All <EMAIL> → <EMAIL>"
echo "- Removed 'Musicians apply' buttons from application sections"
echo "- Simplified contact page to only show General Inquiries"
EOF
