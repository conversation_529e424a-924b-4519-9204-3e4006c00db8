#!/bin/bash

# Chocolate & Art Show - Performance Optimization Script
# This script optimizes images and assets for web deployment

echo "🎨 Chocolate & Art Show - Performance Optimization"
echo "=================================================="

# Check if ImageMagick is available for image optimization
if command -v convert &> /dev/null; then
    echo "✅ ImageMagick found - optimizing images..."
    
    # Optimize gallery images
    echo "📸 Optimizing gallery images..."
    for img in assets/gallery/*.jpg; do
        if [ -f "$img" ]; then
            echo "  Processing: $(basename "$img")"
            # Create optimized version with 85% quality
            convert "$img" -quality 85 -strip "${img%.jpg}_optimized.jpg"
        fi
    done
    
    # Optimize artist images
    echo "🎭 Optimizing artist images..."
    for dir in assets/artists/*/; do
        if [ -d "$dir" ]; then
            for img in "$dir"*.jpg; do
                if [ -f "$img" ]; then
                    echo "  Processing: $(basename "$img")"
                    convert "$img" -quality 85 -strip "${img%.jpg}_optimized.jpg"
                fi
            done
        fi
    done
    
    echo "✅ Image optimization complete!"
else
    echo "⚠️  ImageMagick not found - skipping image optimization"
    echo "   Install with: brew install imagemagick (macOS) or apt-get install imagemagick (Ubuntu)"
fi

# Create .htaccess for Apache servers (like Siteground)
echo "🔧 Creating .htaccess for performance..."
cat > .htaccess << 'EOF'
# Chocolate & Art Show - Performance Optimizations

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "camera=(), microphone=(), geolocation=()"
</IfModule>

# Redirect to HTTPS (uncomment when SSL is set up)
# RewriteEngine On
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Custom error pages
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html
EOF

echo "✅ .htaccess created!"

# Create a simple 404 page
echo "📄 Creating 404 error page..."
cat > 404.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Page Not Found — Chocolate & Art Show</title>
    <link rel="stylesheet" href="/assets/css/global.css">
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }
        .error-content h1 {
            font-size: clamp(3rem, 8vw, 6rem);
            margin-bottom: 1rem;
            color: #1d66ff;
        }
        .error-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: var(--muted);
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #1d66ff;
            color: #fff;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="error-page">
        <div class="error-content">
            <h1>404</h1>
            <p>Oops! This page seems to have wandered off like a chocolate-covered artist.</p>
            <a href="/" class="btn">Return Home</a>
        </div>
    </div>
</body>
</html>
EOF

echo "✅ 404 page created!"

# Create robots.txt
echo "🤖 Creating robots.txt..."
cat > robots.txt << 'EOF'
User-agent: *
Allow: /

Sitemap: https://chocolateandartshow.com/sitemap.xml
EOF

echo "✅ robots.txt created!"

# Create sitemap.xml
echo "🗺️  Creating sitemap.xml..."
cat > sitemap.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://chocolateandartshow.com/</loc>
        <lastmod>2025-08-21</lastmod>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>
    <url>
        <loc>https://chocolateandartshow.com/artists</loc>
        <lastmod>2025-08-21</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.9</priority>
    </url>
    <url>
        <loc>https://chocolateandartshow.com/tickets.html</loc>
        <lastmod>2025-08-21</lastmod>
        <changefreq>daily</changefreq>
        <priority>0.9</priority>
    </url>
    <url>
        <loc>https://chocolateandartshow.com/gallery.html</loc>
        <lastmod>2025-08-21</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    <url>
        <loc>https://chocolateandartshow.com/schedule.html</loc>
        <lastmod>2025-08-21</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    <url>
        <loc>https://chocolateandartshow.com/faq.html</loc>
        <lastmod>2025-08-21</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.7</priority>
    </url>
    <url>
        <loc>https://chocolateandartshow.com/contact.html</loc>
        <lastmod>2025-08-21</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.7</priority>
    </url>
    <url>
        <loc>https://chocolateandartshow.com/artists/danica.html</loc>
        <lastmod>2025-08-21</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.6</priority>
    </url>
    <url>
        <loc>https://chocolateandartshow.com/artists/david-v.html</loc>
        <lastmod>2025-08-21</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.6</priority>
    </url>
    <url>
        <loc>https://chocolateandartshow.com/play/kusama</loc>
        <lastmod>2025-08-21</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.5</priority>
    </url>
</urlset>
EOF

echo "✅ sitemap.xml created!"

echo ""
echo "🎉 Optimization complete!"
echo ""
echo "📋 Next steps for deployment:"
echo "1. Upload all files to your Siteground hosting"
echo "2. Set up SSL certificate"
echo "3. Update domain references in sitemap.xml"
echo "4. Test all pages and functionality"
echo "5. Submit sitemap to Google Search Console"
echo ""
echo "🚀 Your Chocolate & Art Show website is ready!"
EOF
